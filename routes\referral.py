from fastapi import APIRouter, Depends, HTTPException, status
from sqlmodel import Session, select
from typing import List
from datetime import datetime

from models.referral import ReferralPoints, WithdrawalRequest, PointsTransactionType
from models.user import User
from db import get_session
from schemas.referral import (
    PointsResponse,
    WithdrawalRequestCreate,
    WithdrawalRequestResponse,
    ReferralStatsResponse
)
from utils.auth import get_current_user

router = APIRouter(prefix="/referral", tags=["referral"])

# Constants
REFERRAL_BONUS_POINTS = 100  # Points awarded for successful referral
ORDER_BONUS_PERCENTAGE = 0.05  # 5% of order value converted to points

@router.get("/my-points", response_model=PointsResponse)
async def get_my_points(
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    return {"total_points": current_user.total_points}

@router.get("/my-referral-code")
async def get_my_referral_code(current_user: User = Depends(get_current_user)):
    return {"referral_code": current_user.referral_code}

@router.post("/add-points-from-order/{order_value}")
async def add_points_from_order(
    order_value: float,
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    points_earned = order_value * ORDER_BONUS_PERCENTAGE
    
    # Create points transaction
    points_transaction = ReferralPoints(
        user_id=current_user.id,
        points=points_earned,
        transaction_type=PointsTransactionType.ORDER_BONUS,
        amount=order_value,
        description=f"Points earned from order worth {order_value}",
        status="completed"
    )
    
    # Update user's total points
    current_user.total_points += points_earned
    
    session.add(points_transaction)
    session.add(current_user)
    session.commit()
    
    return {"points_earned": points_earned, "total_points": current_user.total_points}

@router.post("/withdraw", response_model=WithdrawalRequestResponse)
async def request_withdrawal(
    withdrawal_request: WithdrawalRequestCreate,
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    if withdrawal_request.points_amount > current_user.total_points:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Insufficient points balance"
        )
    
    # Create withdrawal request
    new_withdrawal = WithdrawalRequest(
        user_id=current_user.id,
        points_amount=withdrawal_request.points_amount,
        withdrawal_method=withdrawal_request.withdrawal_method
    )
    # Set withdrawal details using the new method
    new_withdrawal.set_withdrawal_details(withdrawal_request.withdrawal_details)
    
    # Create points transaction
    points_transaction = ReferralPoints(
        user_id=current_user.id,
        points=-withdrawal_request.points_amount,  # Negative points for withdrawal
        transaction_type=PointsTransactionType.WITHDRAWAL,
        amount=withdrawal_request.points_amount,
        description=f"Withdrawal request for {withdrawal_request.points_amount} points",
        status="pending"
    )
    
    # Update user's total points
    current_user.total_points -= withdrawal_request.points_amount
    
    session.add(new_withdrawal)
    session.add(points_transaction)
    session.add(current_user)
    session.commit()
    session.refresh(new_withdrawal)
    
    # Convert the response to include withdrawal details as dict
    response_data = {
        "id": new_withdrawal.id,
        "user_id": new_withdrawal.user_id,
        "points_amount": new_withdrawal.points_amount,
        "status": new_withdrawal.status,
        "withdrawal_method": new_withdrawal.withdrawal_method,
        "withdrawal_details": new_withdrawal.get_withdrawal_details(),
        "created_at": new_withdrawal.created_at,
        "processed_at": new_withdrawal.processed_at
    }
    return response_data

@router.get("/transactions", response_model=List[ReferralPoints])
async def get_transactions(
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    transactions = session.exec(
        select(ReferralPoints).where(ReferralPoints.user_id == current_user.id)
    ).all()
    return transactions

@router.get("/stats", response_model=ReferralStatsResponse)
async def get_referral_stats(
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    # Get number of successful referrals
    referred_users = session.exec(
        select(User).where(User.referred_by == current_user.referral_code)
    ).all()
    
    # Get total earnings from referrals
    referral_earnings = session.exec(
        select(ReferralPoints).where(
            ReferralPoints.user_id == current_user.id,
            ReferralPoints.transaction_type == PointsTransactionType.REFERRAL_BONUS
        )
    ).all()
    
    total_referral_earnings = sum(t.points for t in referral_earnings)
    
    return {
        "total_referrals": len(referred_users),
        "total_referral_earnings": total_referral_earnings,
        "total_points_balance": current_user.total_points
    } 