from fastapi import APIRouter, Depends, HTTPException, status, Form
from sqlmodel import Session, select
from typing import List
from datetime import datetime
import random

from models.referral import ReferralPoints, WithdrawalRequest, PointsTransactionType, GiveawayEntry
from models.user import User
from db import get_session
from schemas.referral import (
    PointsResponse,
    WithdrawalRequestCreate,
    WithdrawalRequestResponse,
    ReferralStatsResponse,
    GiveawayEntryResponse,
    GiveawayStatsResponse
)
from utils.auth import get_current_user

router = APIRouter(prefix="/referral", tags=["referral"])

# Constants
REFERRAL_BONUS_POINTS = 100  # Points awarded for successful referral
ORDER_BONUS_PERCENTAGE = 0.05  # 5% of order value converted to points

@router.get("/my-points", response_model=PointsResponse)
async def get_my_points(
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    return {"total_points": current_user.total_points}

@router.get("/my-referral-code")
async def get_my_referral_code(current_user: User = Depends(get_current_user)):
    return {"referral_code": current_user.referral_code}

@router.post("/add-points-from-order/{order_value}")
async def add_points_from_order(
    order_value: float,
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    points_earned = order_value * ORDER_BONUS_PERCENTAGE
    
    # Create points transaction
    points_transaction = ReferralPoints(
        user_id=current_user.id,
        points=points_earned,
        transaction_type=PointsTransactionType.ORDER_BONUS,
        amount=order_value,
        description=f"Points earned from order worth {order_value}",
        status="completed"
    )
    
    # Update user's total points
    current_user.total_points += points_earned
    
    session.add(points_transaction)
    session.add(current_user)
    session.commit()
    
    return {"points_earned": points_earned, "total_points": current_user.total_points}

@router.post("/withdraw", response_model=WithdrawalRequestResponse)
async def request_withdrawal(
    withdrawal_request: WithdrawalRequestCreate,
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    if withdrawal_request.points_amount > current_user.total_points:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Insufficient points balance"
        )
    
    # Create withdrawal request
    new_withdrawal = WithdrawalRequest(
        user_id=current_user.id,
        points_amount=withdrawal_request.points_amount,
        withdrawal_method=withdrawal_request.withdrawal_method
    )
    # Set withdrawal details using the new method
    new_withdrawal.set_withdrawal_details(withdrawal_request.withdrawal_details)
    
    # Create points transaction
    points_transaction = ReferralPoints(
        user_id=current_user.id,
        points=-withdrawal_request.points_amount,  # Negative points for withdrawal
        transaction_type=PointsTransactionType.WITHDRAWAL,
        amount=withdrawal_request.points_amount,
        description=f"Withdrawal request for {withdrawal_request.points_amount} points",
        status="pending"
    )
    
    # Update user's total points
    current_user.total_points -= withdrawal_request.points_amount
    
    session.add(new_withdrawal)
    session.add(points_transaction)
    session.add(current_user)
    session.commit()
    session.refresh(new_withdrawal)
    
    # Convert the response to include withdrawal details as dict
    response_data = {
        "id": new_withdrawal.id,
        "user_id": new_withdrawal.user_id,
        "points_amount": new_withdrawal.points_amount,
        "status": new_withdrawal.status,
        "withdrawal_method": new_withdrawal.withdrawal_method,
        "withdrawal_details": new_withdrawal.get_withdrawal_details(),
        "created_at": new_withdrawal.created_at,
        "processed_at": new_withdrawal.processed_at
    }
    return response_data

@router.get("/transactions", response_model=List[ReferralPoints])
async def get_transactions(
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    transactions = session.exec(
        select(ReferralPoints).where(ReferralPoints.user_id == current_user.id)
    ).all()
    return transactions

@router.get("/stats", response_model=ReferralStatsResponse)
async def get_referral_stats(
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    # Get number of successful referrals
    referred_users = session.exec(
        select(User).where(User.referred_by == current_user.referral_code)
    ).all()
    
    # Get total earnings from referrals
    referral_earnings = session.exec(
        select(ReferralPoints).where(
            ReferralPoints.user_id == current_user.id,
            ReferralPoints.transaction_type == PointsTransactionType.REFERRAL_BONUS
        )
    ).all()
    
    total_referral_earnings = sum(t.points for t in referral_earnings)
    
    return {
        "total_referrals": len(referred_users),
        "total_referral_earnings": total_referral_earnings,
        "total_points_balance": current_user.total_points
    }

# ==================== GIVEAWAY ENDPOINTS ====================

@router.get("/giveaway/emails")
async def get_giveaway_emails(session: Session = Depends(get_session)):
    """
    Get all giveaway participant emails (admin endpoint)
    """
    giveaway_entries = session.exec(select(GiveawayEntry)).all()
    
    emails = []
    for entry in giveaway_entries:
        emails.append({
            "email": entry.email,
            "account_size": entry.account_size,
            "order_value": entry.order_value,
            "created_at": entry.created_at,
            "is_winner": entry.is_winner
        })
    
    return {
        "total_participants": len(emails),
        "emails": emails
    }

@router.get("/giveaway/entries")
async def get_giveaway_entries(session: Session = Depends(get_session)):
    """
    Get all giveaway entries (admin endpoint)
    """
    giveaway_entries = session.exec(select(GiveawayEntry)).all()
    
    entries = []
    total_prize_value = 0
    
    for entry in giveaway_entries:
        entries.append({
            "id": entry.id,
            "user_id": entry.user_id,
            "order_id": entry.order_id,
            "email": entry.email,
            "account_size": entry.account_size,
            "order_value": entry.order_value,
            "created_at": entry.created_at,
            "is_winner": entry.is_winner,
            "prize_amount": entry.prize_amount
        })
        
        if entry.is_winner and entry.prize_amount:
            total_prize_value += entry.prize_amount
    
    return {
        "total_entries": len(entries),
        "total_eligible_orders": len(entries),
        "total_prize_value": total_prize_value,
        "entries": entries
    }

@router.get("/giveaway/my-entries")
async def get_my_giveaway_entries(
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
):
    """
    Get current user's giveaway entries
    """
    giveaway_entries = session.exec(
        select(GiveawayEntry).where(GiveawayEntry.user_id == current_user.id)
    ).all()
    
    entries = []
    for entry in giveaway_entries:
        entries.append({
            "id": entry.id,
            "order_id": entry.order_id,
            "account_size": entry.account_size,
            "order_value": entry.order_value,
            "created_at": entry.created_at,
            "is_winner": entry.is_winner,
            "prize_amount": entry.prize_amount
        })
    
    return {
        "total_entries": len(entries),
        "entries": entries
    }

@router.post("/giveaway/select-winner")
async def select_giveaway_winner(
    session: Session = Depends(get_session)
):
    """
    Randomly select a giveaway winner (admin endpoint)
    """
    # Get all non-winner entries
    eligible_entries = session.exec(
        select(GiveawayEntry).where(GiveawayEntry.is_winner == False)
    ).all()
    
    if not eligible_entries:
        raise HTTPException(
            status_code=404,
            detail="No eligible entries found for giveaway"
        )
    
    # Randomly select a winner
    winner = random.choice(eligible_entries)
    
    # Calculate prize amount (you can customize this logic)
    prize_amount = winner.order_value * 0.1  # 10% of order value as prize
    
    # Mark as winner
    winner.is_winner = True
    winner.prize_amount = prize_amount
    
    session.add(winner)
    session.commit()
    
    return {
        "message": "Giveaway winner selected successfully",
        "winner": {
            "email": winner.email,
            "account_size": winner.account_size,
            "order_value": winner.order_value,
            "prize_amount": prize_amount
        }
    }

@router.post("/giveaway/select-multiple-winners")
async def select_multiple_giveaway_winners(
    num_winners: int = Form(...),
    session: Session = Depends(get_session)
):
    """
    Select multiple giveaway winners (admin endpoint)
    """
    # Get all non-winner entries
    eligible_entries = session.exec(
        select(GiveawayEntry).where(GiveawayEntry.is_winner == False)
    ).all()
    
    if not eligible_entries:
        raise HTTPException(
            status_code=404,
            detail="No eligible entries found for giveaway"
        )
    
    if num_winners > len(eligible_entries):
        num_winners = len(eligible_entries)
    
    # Randomly select winners
    winners = random.sample(eligible_entries, num_winners)
    
    selected_winners = []
    
    for winner in winners:
        # Calculate prize amount (you can customize this logic)
        prize_amount = winner.order_value * 0.1  # 10% of order value as prize
        
        # Mark as winner
        winner.is_winner = True
        winner.prize_amount = prize_amount
        
        session.add(winner)
        
        selected_winners.append({
            "email": winner.email,
            "account_size": winner.account_size,
            "order_value": winner.order_value,
            "prize_amount": prize_amount
        })
    
    session.commit()
    
    return {
        "message": f"{len(selected_winners)} giveaway winners selected successfully",
        "winners": selected_winners
    }

@router.get("/giveaway/winners")
async def get_giveaway_winners(session: Session = Depends(get_session)):
    """
    Get all giveaway winners
    """
    winners = session.exec(
        select(GiveawayEntry).where(GiveawayEntry.is_winner == True)
    ).all()
    
    winner_list = []
    total_prize_value = 0
    
    for winner in winners:
        winner_list.append({
            "email": winner.email,
            "account_size": winner.account_size,
            "order_value": winner.order_value,
            "prize_amount": winner.prize_amount,
            "created_at": winner.created_at
        })
        
        if winner.prize_amount:
            total_prize_value += winner.prize_amount
    
    return {
        "total_winners": len(winner_list),
        "total_prize_value": total_prize_value,
        "winners": winner_list
    }

@router.delete("/giveaway/reset")
async def reset_giveaway(session: Session = Depends(get_session)):
    """
    Reset all giveaway entries (admin endpoint) - use with caution!
    """
    # Delete all giveaway entries
    session.exec(select(GiveawayEntry)).delete()
    session.commit()
    
    return {
        "message": "Giveaway reset successfully. All entries have been cleared."
    } 