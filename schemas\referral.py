from pydantic import BaseModel
from typing import Dict, Optional, List
from datetime import datetime

class PointsResponse(BaseModel):
    total_points: float

class WithdrawalRequestCreate(BaseModel):
    points_amount: float
    withdrawal_method: str  # e.g., "bank_transfer", "paypal"
    withdrawal_details: Dict[str, str]  # Flexible structure for different withdrawal methods

class WithdrawalRequestResponse(BaseModel):
    id: int
    user_id: int
    points_amount: float
    status: str
    withdrawal_method: str
    withdrawal_details: Dict[str, str]
    created_at: datetime
    processed_at: Optional[datetime]

class ReferralStatsResponse(BaseModel):
    total_referrals: int
    total_referral_earnings: float
    total_points_balance: float

class GiveawayEntryResponse(BaseModel):
    id: int
    user_id: int
    order_id: int
    email: str
    account_size: str
    order_value: float
    created_at: datetime
    is_winner: bool
    prize_amount: Optional[float]

class GiveawayStatsResponse(BaseModel):
    total_entries: int
    total_eligible_orders: int
    total_prize_value: float
    entries: List[GiveawayEntryResponse] 