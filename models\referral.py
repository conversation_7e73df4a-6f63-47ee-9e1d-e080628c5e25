from sqlmodel import SQLMode<PERSON>, Field, Relationship
from datetime import datetime
from typing import Optional, List, TYPE_CHECKING
from enum import Enum
import json

if TYPE_CHECKING:
    from .user import User

class PointsTransactionType(str, Enum):
    REFERRAL_BONUS = "referral_bonus"
    ORDER_BONUS = "order_bonus"
    WITHDRAWAL = "withdrawal"

class ReferralPoints(SQLModel, table=True):
    id: int | None = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="user.id")
    points: float = Field(default=0)
    transaction_type: PointsTransactionType
    amount: float
    description: str
    status: str = Field(default="pending")  # pending, completed, failed
    created_at: datetime = Field(default_factory=datetime.utcnow)
    
    # Relationship
    user: Optional["User"] = Relationship(back_populates="referral_points")

class WithdrawalRequest(SQLModel, table=True):
    id: int | None = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="user.id")
    points_amount: float
    status: str = Field(default="pending")  # pending, approved, rejected
    withdrawal_method: str  # e.g., "bank_transfer", "paypal"
    withdrawal_details: str = Field(default="")  # JSON string storing withdrawal details
    created_at: datetime = Field(default_factory=datetime.utcnow)
    processed_at: Optional[datetime] = None
    
    # Relationship
    user: Optional["User"] = Relationship(back_populates="withdrawal_requests")
    
    def set_withdrawal_details(self, details: dict):
        """Convert dictionary to JSON string for storage"""
        self.withdrawal_details = json.dumps(details)
    
    def get_withdrawal_details(self) -> dict:
        """Convert stored JSON string back to dictionary"""
        if not self.withdrawal_details:
            return {}
        return json.loads(self.withdrawal_details)

class GiveawayEntry(SQLModel, table=True):
    id: int | None = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="user.id")
    order_id: int = Field(foreign_key="ordermodel.id")
    email: str
    account_size: str
    order_value: float  # The actual numeric value of the order
    created_at: datetime = Field(default_factory=datetime.utcnow)
    is_winner: bool = Field(default=False)  # To mark if they won the giveaway
    prize_amount: Optional[float] = None  # Prize amount if they win
    
    # Relationships
    user: Optional["User"] = Relationship(back_populates="giveaway_entries") 